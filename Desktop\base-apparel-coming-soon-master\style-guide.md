# Front-end Style Guide

## Layout

The designs were created to the following widths:

- Mobile: 375px
- Desktop: 1440px

> 💡 These are just the design sizes. Ensure content is responsive and meets WCAG requirements by testing the full range of screen sizes from 320px to large screens.

## Colors

### Primary

- Pink 400: hsl(0, 36%, 70%)
- Red 500: hsl(0, 93%, 68%)

### Neutral

- Gray 900: hsl(0, 6%, 24%)

### Gradients

- Linear, 135deg, from hsl(0, 0%, 100%), to hsl(0, 100%, 98%)
- Linear, 135deg, from hsl(0, 80%, 86%), to hsl(0, 74%, 74%)

## Typography

### Body Copy

- Font size: 16px

### Font

- Family: [<PERSON><PERSON>](https://fonts.google.com/specimen/Josefin+Sans)
- Weights: 300, 400, 600

> 💎 [Upgrade to Pro](https://www.frontendmentor.io/pro?ref=style-guide) for design file access to see all design details and get hands-on experience using a professional workflow with tools like Figma.
